//
//  ExerciseWorkoutView.swift
//  FitBreak
//
//  Created by <PERSON> on 16/09/2025.
//

import SwiftUI
import AVKit
import Combine

struct ExerciseWorkoutView: View {
    @ObservedObject var timerManager: TimerManager
    @StateObject private var mediaManager = MediaPlayerManager()
    @Environment(\.dismiss) private var dismiss
    
    @State private var currentMotivationIndex = 0
    @State private var motivationTimer: Timer?
    @State private var cancellables = Set<AnyCancellable>()
    
    // Motivational messages that rotate during workout
    private let motivationalMessages = [
        "You're doing great! Keep moving! 💪",
        "Feel that energy boost! ⚡",
        "Every jump counts! 🌟",
        "You're stronger than you think! 🔥",
        "Break time = active time! 🎯",
        "Your body will thank you! ❤️",
        "Movement is medicine! 🏃‍♀️",
        "You've got this! 🚀",
        "Energize your mind and body! ✨",
        "Small breaks, big benefits! 🌈"
    ]
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Full-screen video background (bottom layer)
                fullScreenVideoBackground(geometry: geometry)

                // Gradient overlay for UI readability
                gradientOverlay

                // UI elements overlay (top layer)
                VStack(spacing: 0) {
                    // Header with close button (top positioned)
                    headerView
                        .padding(.horizontal)
                        .padding(.top)

                    // Spacer to push bottom UI elements down (creates video viewing area)
                    Spacer()

                    // Bottom UI overlay (bottom 40% of screen)
                    bottomUIOverlay
                        .padding(.horizontal)
                        .padding(.bottom)
                }
            }
        }
        .preferredColorScheme(.dark)
        .onAppear {
            setupWorkout()
        }
        .onDisappear {
            cleanup()
        }
        .accessibilityElement(children: .contain)
        .accessibilityLabel("Exercise workout screen")
    }
    
    // MARK: - Full-Screen Video Background

    private func fullScreenVideoBackground(geometry: GeometryProxy) -> some View {
        Group {
            if let player = mediaManager.player {
                VideoPlayer(player: player)
                    .frame(
                        width: UIScreen.main.bounds.width,
                        height: UIScreen.main.bounds.height
                    )
                    .position(
                        x: UIScreen.main.bounds.width / 2,
                        y: UIScreen.main.bounds.height / 2 - 80
                    )
                    .clipped()
                    .ignoresSafeArea(.all, edges: .all)
                    .accessibilityLabel("Jumping jacks demonstration video")
                    .accessibilityHint("Full screen exercise demonstration playing in background")
            } else {
                Rectangle()
                    .fill(Color.black)
                    .frame(
                        width: UIScreen.main.bounds.width,
                        height: UIScreen.main.bounds.height
                    )
                    .position(
                        x: UIScreen.main.bounds.width / 2,
                        y: UIScreen.main.bounds.height / 2
                    )
                    .ignoresSafeArea(.all, edges: .all)
                    .overlay(
                        VStack {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(1.5)
                            Text("Loading exercise video...")
                                .font(.headline)
                                .foregroundColor(.white.opacity(0.8))
                                .padding(.top, 16)
                        }
                    )
                    .accessibilityLabel("Loading exercise demonstration")
            }
        }
    }

    // MARK: - Gradient Overlay

    private var gradientOverlay: some View {
        LinearGradient(
            stops: [
                .init(color: .clear, location: 0.0),
                .init(color: .clear, location: 0.6),
                .init(color: Color.purple.opacity(0.3), location: 0.75),
                .init(color: Color.purple.opacity(0.8), location: 0.9),
                .init(color: Color.blue.opacity(0.6), location: 1.0)
            ],
            startPoint: .top,
            endPoint: .bottom
        )
        .frame(
            width: UIScreen.main.bounds.width,
            height: UIScreen.main.bounds.height
        )
        .position(
            x: UIScreen.main.bounds.width / 2,
            y: UIScreen.main.bounds.height / 2
        )
        .ignoresSafeArea(.all, edges: .all)
    }

    // MARK: - Header View

    private var headerView: some View {
        HStack {
            Button(action: {
                // Announce for VoiceOver users
                AccessibilityHelper.announce("Ending workout early")
                dismiss()
            }) {
                Image(systemName: "xmark")
                    .font(.title2)
                    .foregroundColor(.white)
                    .frame(width: 44, height: 44)
                    .background(Circle().fill(Color.black.opacity(0.4)))
            }
            .accessibilityLabel("End workout early")
            .accessibilityHint("Double tap to stop exercise and return to timer")

            Spacer()

//            Text("Active Break")
//                .font(.headline)
//                .fontWeight(.semibold)
//                .foregroundColor(.white)
//                .accessibilityAddTraits(.isHeader)

            Spacer()

            // Placeholder for symmetry
            Color.clear
                .frame(width: 44, height: 44)
        }
    }
    
    // MARK: - Bottom UI Overlay

    private var bottomUIOverlay: some View {
        VStack(spacing: 20) {
            // Timer display
            timerDisplayView

            // Motivational message
            motivationalMessageView

            // Control buttons
            controlButtonsView
        }
    }

    // MARK: - Timer Display

    private var timerDisplayView: some View {
        VStack(spacing: 8) {
            Text("Time Remaining")
                .font(.caption)
                .foregroundColor(.white.opacity(0.7))
                .textCase(.uppercase)
                .tracking(1)

            Text(timerManager.formattedTimeRemaining)
                .font(.system(size: 48, weight: .light, design: .monospaced))
                .foregroundColor(.white)
                .dynamicTypeSize(minScale: 0.7, maxScale: 1.2)
                .timerDisplayAccessibility(timeRemaining: timerManager.timeRemaining, sessionType: timerManager.currentSessionType)

            // Progress bar
            ProgressView(value: timerManager.progress)
                .progressViewStyle(LinearProgressViewStyle(tint: .green))
                .frame(height: 8)
                .background(Color.white.opacity(0.2))
                .cornerRadius(4)
                .padding(.horizontal, 20)
                .accessibilityLabel("Break progress: \(Int(timerManager.progress * 100)) percent complete")
        }
        .padding(.vertical, 16)
        .padding(.horizontal, 20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.black.opacity(0.3))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
    

    
    // MARK: - Motivational Message
    
    private var motivationalMessageView: some View {
        VStack(spacing: 12) {
            Text("💪")
                .font(.system(size: 32))
                .accessibilityHidden(true)
            
            Text(motivationalMessages[currentMotivationIndex])
                .font(.title3)
                .fontWeight(.medium)
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
                .motivationalMessageAccessibility(motivationalMessages[currentMotivationIndex])
        }
        .frame(minHeight: 80)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.black.opacity(0.2))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
        .animation(.easeInOut(duration: 0.5), value: currentMotivationIndex)
    }
    
    // MARK: - Control Buttons
    
    private var controlButtonsView: some View {
        HStack(spacing: 20) {
            // Pause/Resume timer button
            Button(action: {
                if timerManager.currentState == .running {
                    timerManager.pauseTimer()
                    mediaManager.pauseVideo()
                    AccessibilityHelper.announce("Workout paused")
                } else if timerManager.currentState == .paused {
                    timerManager.resumeTimer()
                    mediaManager.playVideo(loop: true)
                    AccessibilityHelper.announce("Workout resumed")
                }
            }) {
                HStack(spacing: 8) {
                    Image(systemName: timerManager.currentState == .running ? "pause.fill" : "play.fill")
                        .font(.title3)
                    
                    Text(timerManager.currentState == .running ? "Pause" : "Resume")
                        .font(.headline)
                        .fontWeight(.medium)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.orange.opacity(0.9))
                        .shadow(color: .black.opacity(0.3), radius: 4, x: 0, y: 2)
                )
            }
            .accessibilityLabel(timerManager.currentState == .running ? "Pause workout" : "Resume workout")
            .accessibilityHint("Double tap to \(timerManager.currentState == .running ? "pause" : "resume") the break timer and exercise")
            
            // Skip break button
            Button(action: {
                AccessibilityHelper.announce("Skipping break")
                timerManager.skipSession()
                dismiss()
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "forward.fill")
                        .font(.title3)
                    
                    Text("Skip")
                        .font(.headline)
                        .fontWeight(.medium)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.red.opacity(0.9))
                        .shadow(color: .black.opacity(0.3), radius: 4, x: 0, y: 2)
                )
            }
            .accessibilityLabel("Skip break")
            .accessibilityHint("Double tap to end break early and return to work session")
        }
        .padding(.horizontal)
    }
    
    // MARK: - Setup and Cleanup
    
    private func setupWorkout() {
        print("🏃‍♀️ ExerciseWorkoutView - setupWorkout called")
        print("  - Current session type: \(timerManager.currentSessionType)")
        print("  - Current state: \(timerManager.currentState)")
        print("  - Time remaining: \(timerManager.timeRemaining)")
        print("  - Total duration: \(timerManager.totalDuration)")

        // Ensure we're in a break session and start the timer if needed
        if timerManager.currentSessionType != .work && timerManager.currentState == .idle {
            print("🏃‍♀️ ExerciseWorkoutView - Starting break timer")
            timerManager.startTimer()
        } else if timerManager.currentState == .completed {
            // If the timer is completed, we need to start the next session (break)
            print("🏃‍♀️ ExerciseWorkoutView - Timer completed, starting next session")
            timerManager.startNextSession()
            timerManager.startTimer()
        } else {
            print("🏃‍♀️ ExerciseWorkoutView - Timer state: \(timerManager.currentState), session: \(timerManager.currentSessionType)")
        }

        // Load and start video
        mediaManager.loadVideo(filename: "jumping-jacks")

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            mediaManager.playVideo(loop: true)
        }

        // Start motivational message rotation
        startMotivationRotation()

        // Monitor timer completion
        observeTimerCompletion()

        // Announce screen for VoiceOver users
        AccessibilityHelper.announceScreenChange(
            "Exercise workout started. Follow along with the video demonstration.",
            delay: 1.0
        )
    }
    
    private func startMotivationRotation() {
        motivationTimer = Timer.scheduledTimer(withTimeInterval: 8.0, repeats: true) { _ in
            withAnimation(.easeInOut(duration: 0.5)) {
                currentMotivationIndex = (currentMotivationIndex + 1) % motivationalMessages.count
            }
        }
    }
    
    private func observeTimerCompletion() {
        timerManager.$currentState
            .sink { state in
                print("🏃‍♀️ ExerciseWorkoutView - Timer state changed to: \(state)")
                if state == .completed {
                    print("🏃‍♀️ ExerciseWorkoutView - Break completed, dismissing workout view")
                    // Break completed, dismiss workout view
                    AccessibilityHelper.announce(
                        "Break completed! Great job on staying active.",
                        delay: 1.0
                    )
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                        self.dismiss()
                    }
                }
            }
            .store(in: &cancellables)
    }
    
    private func cleanup() {
        mediaManager.stopVideo()
        motivationTimer?.invalidate()
        motivationTimer = nil
        cancellables.removeAll()
    }
}

#Preview {
    ExerciseWorkoutView(timerManager: TimerManager())
}
