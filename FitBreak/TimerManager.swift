//
//  TimerManager.swift
//  FitBreak
//
//  Created by <PERSON> on 16/09/2025.
//

import Foundation
import Combine
import UserNotifications
import AVFoundation
import AudioToolbox
import UIKit

enum TimerState {
    case idle
    case running
    case paused
    case completed
}

enum SessionType: String, CaseIterable {
    case work = "work"
    case shortBreak = "shortBreak"
    case longBreak = "longBreak"
    
    var displayName: String {
        switch self {
        case .work:
            return "Focus Session"
        case .shortBreak:
            return "Short Break"
        case .longBreak:
            return "Long Break"
        }
    }
    
    var emoji: String {
        switch self {
        case .work:
            return "🍅"
        case .shortBreak:
            return "☕️"
        case .longBreak:
            return "🌟"
        }
    }
}

@MainActor
class TimerManager: ObservableObject {
    @Published var currentState: TimerState = .idle
    @Published var currentSessionType: SessionType = .work
    @Published var timeRemaining: TimeInterval = 1500 // 25 minutes default
    @Published var totalDuration: TimeInterval = 1500
    @Published var completedPomodoros: Int = 0
    @Published var currentTask: Task?
    @Published var progress: Double = 0.0
    @Published var shouldShowFit<PERSON>View: Bool = false
    
    private var timer: Timer?
    private var backgroundTaskID: UIBackgroundTaskIdentifier = .invalid
    private let persistenceController = PersistenceController.shared
    private var settings: UserSettings
    private var audioPlayer: AVAudioPlayer?
    
    init() {
        self.settings = persistenceController.getUserSettings()
        self.timeRemaining = TimeInterval(settings.workSessionDuration)
        self.totalDuration = TimeInterval(settings.workSessionDuration)

        setupAudioSession()
        requestNotificationPermission()
        setupAppLifecycleObservers()
    }
    
    // MARK: - Timer Controls
    
    func startTimer() {
        guard currentState != .running else { return }
        
        currentState = .running
        startBackgroundTask()
        
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            DispatchQueue.main.async {
                self?.updateTimer()
            }
        }
        
        scheduleNotification()
    }
    
    func pauseTimer() {
        guard currentState == .running else { return }
        
        currentState = .paused
        timer?.invalidate()
        timer = nil
        endBackgroundTask()
        cancelNotifications()
    }
    
    func resumeTimer() {
        guard currentState == .paused else { return }
        startTimer()
    }
    
    func resetTimer() {
        timer?.invalidate()
        timer = nil
        currentState = .idle
        endBackgroundTask()
        cancelNotifications()
        
        timeRemaining = totalDuration
        progress = 0.0
    }
    
    func skipSession() {
        completeCurrentSession(wasSkipped: true)
    }
    
    private func updateTimer() {
        timeRemaining -= 1
        progress = 1.0 - (timeRemaining / totalDuration)
        
        if timeRemaining <= 0 {
            completeCurrentSession(wasSkipped: false)
        }
    }
    
    private func completeCurrentSession(wasSkipped: Bool) {
        timer?.invalidate()
        timer = nil
        currentState = .completed
        endBackgroundTask()
        
        // Save session to Core Data
        let duration = Int32(totalDuration)
        _ = persistenceController.createPomodoroSession(
            type: currentSessionType.rawValue,
            duration: duration,
            task: currentTask,
            completed: !wasSkipped
        )
        
        // Update completed pomodoros count
        if currentSessionType == .work && !wasSkipped {
            completedPomodoros += 1
        }
        
        // Play completion sound and haptic feedback
        playCompletionSound()
        triggerHapticFeedback()
        
        // Show fitness view when work session completes and next session is short break
        if currentSessionType == .work && !wasSkipped && settings.fitnessFeatureEnabled {
            let nextSessionType = determineNextSessionType()
            print("🏃‍♀️ Fitness Debug - Work session completed:")
            print("  - Current session: \(currentSessionType)")
            print("  - Next session: \(nextSessionType)")
            print("  - Was skipped: \(wasSkipped)")
            print("  - Fitness enabled: \(settings.fitnessFeatureEnabled)")
            print("  - Completed pomodoros: \(completedPomodoros)")

            if nextSessionType == .shortBreak {
                print("  - ✅ Triggering fitness view!")
                shouldShowFitnessView = true
            } else {
                print("  - ❌ Next session is not short break, no fitness view")
            }
        } else {
            print("🏃‍♀️ Fitness Debug - Conditions not met:")
            print("  - Current session: \(currentSessionType)")
            print("  - Was skipped: \(wasSkipped)")
            print("  - Fitness enabled: \(settings.fitnessFeatureEnabled)")
        }

        // Auto-start next session if enabled
        if shouldAutoStartNextSession() {
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                self.startNextSession()
            }
        }
    }
    
    // MARK: - Session Management
    
    func startNextSession() {
        let nextSessionType = determineNextSessionType()
        startSession(type: nextSessionType)
    }
    
    func startSession(type: SessionType) {
        currentSessionType = type
        
        switch type {
        case .work:
            totalDuration = TimeInterval(settings.workSessionDuration)
        case .shortBreak:
            totalDuration = TimeInterval(settings.shortBreakDuration)
        case .longBreak:
            totalDuration = TimeInterval(settings.longBreakDuration)
        }
        
        timeRemaining = totalDuration
        progress = 0.0
        currentState = .idle
    }
    
    private func determineNextSessionType() -> SessionType {
        switch currentSessionType {
        case .work:
            // Long break after completing the specified number of pomodoros (but not on the first break)
            let longBreakInterval = Int(settings.pomodorosUntilLongBreak)
            let shouldBeLongBreak = completedPomodoros > 0 && completedPomodoros % longBreakInterval == 0

            print("🔄 Session Type Debug:")
            print("  - Completed pomodoros: \(completedPomodoros)")
            print("  - Long break interval: \(longBreakInterval)")
            print("  - Should be long break: \(shouldBeLongBreak)")

            if shouldBeLongBreak {
                print("  - Next session: LONG BREAK")
                return .longBreak
            } else {
                print("  - Next session: SHORT BREAK")
                return .shortBreak
            }
        case .shortBreak, .longBreak:
            print("  - Next session: WORK")
            return .work
        }
    }
    
    private func shouldAutoStartNextSession() -> Bool {
        switch currentSessionType {
        case .work:
            return settings.autoStartBreaks
        case .shortBreak, .longBreak:
            return settings.autoStartSessions
        }
    }
    
    // MARK: - Task Management
    
    func setCurrentTask(_ task: Task?) {
        currentTask = task
    }

    func dismissFitnessView() {
        shouldShowFitnessView = false
    }

    // MARK: - Debug Methods

//    func testFitnessFeature() {
//        print("🧪 Testing fitness feature manually...")
//        print("  - Fitness enabled: \(settings.fitnessFeatureEnabled)")
//        shouldShowFitnessView = true
//    }
    
    // MARK: - Settings
    
    func updateSettings() {
        settings = persistenceController.getUserSettings()
        
        // Update current session duration if timer is idle
        if currentState == .idle {
            switch currentSessionType {
            case .work:
                totalDuration = TimeInterval(settings.workSessionDuration)
            case .shortBreak:
                totalDuration = TimeInterval(settings.shortBreakDuration)
            case .longBreak:
                totalDuration = TimeInterval(settings.longBreakDuration)
            }
            timeRemaining = totalDuration
        }
    }
    
    // MARK: - Formatting
    
    func formattedTime(_ time: TimeInterval) -> String {
        let minutes = Int(time) / 60
        let seconds = Int(time) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
    
    var formattedTimeRemaining: String {
        formattedTime(timeRemaining)
    }
    
    // MARK: - Background Task Management

    private func startBackgroundTask() {
        backgroundTaskID = UIApplication.shared.beginBackgroundTask { [weak self] in
            self?.endBackgroundTask()
        }
    }

    private func endBackgroundTask() {
        if backgroundTaskID != .invalid {
            UIApplication.shared.endBackgroundTask(backgroundTaskID)
            backgroundTaskID = .invalid
        }
    }

    // MARK: - Notifications

    private func requestNotificationPermission() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .sound, .badge]) { granted, error in
            if let error = error {
                print("Notification permission error: \(error)")
            }
        }
    }

    private func scheduleNotification() {
        guard settings.notificationsEnabled else { return }

        let content = UNMutableNotificationContent()
        content.title = currentSessionType.displayName
        content.body = getNotificationMessage()
        content.sound = UNNotificationSound.default

        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: timeRemaining, repeats: false)
        let request = UNNotificationRequest(identifier: "timer_completion", content: content, trigger: trigger)

        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Notification scheduling error: \(error)")
            }
        }
    }

    private func cancelNotifications() {
        UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: ["timer_completion"])
    }

    private func getNotificationMessage() -> String {
        switch currentSessionType {
        case .work:
            return "Great work! Time for a break."
        case .shortBreak:
            return "Break's over! Ready for another focus session?"
        case .longBreak:
            return "Long break complete! You've earned it."
        }
    }

    // MARK: - Audio and Haptics

    private func setupAudioSession() {
        do {
            try AVAudioSession.sharedInstance().setCategory(.playback, mode: .default, options: [.mixWithOthers])
            try AVAudioSession.sharedInstance().setActive(true)
        } catch {
            print("Audio session setup error: \(error)")
        }
    }

    private func playCompletionSound() {
        guard let soundURL = Bundle.main.url(forResource: "completion", withExtension: "mp3") else {
            // Fallback to system sound
            AudioServicesPlaySystemSound(1016) // System notification sound
            return
        }

        do {
            audioPlayer = try AVAudioPlayer(contentsOf: soundURL)
            audioPlayer?.play()
        } catch {
            print("Audio playback error: \(error)")
            AudioServicesPlaySystemSound(1016)
        }
    }

    private func triggerHapticFeedback() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }

    // MARK: - App Lifecycle Management

    private func setupAppLifecycleObservers() {
        NotificationCenter.default.addObserver(
            forName: UIApplication.didEnterBackgroundNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            DispatchQueue.main.async {
                self?.handleAppDidEnterBackground()
            }
        }

        NotificationCenter.default.addObserver(
            forName: UIApplication.willEnterForegroundNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            DispatchQueue.main.async {
                self?.handleAppWillEnterForeground()
            }
        }
    }

    private func handleAppDidEnterBackground() {
        if currentState == .running {
            // Store the time when app went to background
            UserDefaults.standard.set(Date(), forKey: "backgroundTime")
            UserDefaults.standard.set(timeRemaining, forKey: "timeRemainingWhenBackgrounded")
        }
    }

    private func handleAppWillEnterForeground() {
        if currentState == .running,
           let backgroundTime = UserDefaults.standard.object(forKey: "backgroundTime") as? Date,
           let storedTimeRemaining = UserDefaults.standard.object(forKey: "timeRemainingWhenBackgrounded") as? TimeInterval {

            let timeInBackground = Date().timeIntervalSince(backgroundTime)
            let newTimeRemaining = max(0, storedTimeRemaining - timeInBackground)

            if newTimeRemaining <= 0 {
                // Timer completed while in background
                timeRemaining = 0
                completeCurrentSession(wasSkipped: false)
            } else {
                timeRemaining = newTimeRemaining
                progress = 1.0 - (timeRemaining / totalDuration)
            }

            // Clean up stored values
            UserDefaults.standard.removeObject(forKey: "backgroundTime")
            UserDefaults.standard.removeObject(forKey: "timeRemainingWhenBackgrounded")
        }
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}
